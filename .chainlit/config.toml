[project]
# 是否启用遥测（默认：true）。不会收集个人数据。
enable_telemetry = true

# 每个用户使用应用程序需要提供的环境变量列表。
user_env = []

# 连接丢失时会话保存的持续时间（秒）
session_timeout = 3600

# 用户会话过期的持续时间（秒）
user_session_timeout = 1296000  # 15 天

# 启用第三方缓存（例如，LangChain 缓存）
cache = false

# 授权来源
allow_origins = ["*"]

[features]
# 在消息中处理和显示 HTML。这可能存在安全风险（参见 https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript）
unsafe_allow_html = false

# 处理和显示数学表达式。这可能与消息中的 "$" 字符冲突。
latex = false

# 在窗口顶部自动滚动新用户消息
user_message_autoscroll = true

# 自动为线程标记当前聊天配置文件（如果使用聊天配置文件）
auto_tag_thread = true

# 允许用户编辑自己的消息
edit_message = true

# 授权用户随消息自发上传文件
[features.spontaneous_file_upload]
    enabled = true
    # 使用 MIME 类型定义接受的文件类型
    # 示例：
    # 1. 特定文件类型：
    #    accept = ["image/jpeg", "image/png", "application/pdf"]
    # 2. 某种类型的所有文件：
    #    accept = ["image/*", "audio/*", "video/*"]
    # 3. 特定文件扩展名：
    #    accept = { "application/octet-stream" = [".xyz", ".pdb"] }
    # 注意：不建议使用 "*/*"，因为它可能导致浏览器警告
    accept = ["*/*"]
    max_files = 20
    max_size_mb = 500

[features.audio]
    # 音频采样率
    sample_rate = 24000

[features.mcp.sse]
    enabled = true

[features.mcp.stdio]
    enabled = true
    # Only the executables in the allow list can be used for MCP stdio server.
    # Only need the base name of the executable, e.g. "npx", not "/usr/bin/npx".
    # Please don't comment this line for now, we need it to parse the executable name.
    allowed_executables = [ "npx", "uvx" ]

[UI]
# Name of the assistant.
name = "LangGraph Agent"

# default_theme = "dark"

layout = "wide"

# default_sidebar_state = "open"

# Description of the assistant. This is used for HTML tags.
# description = ""

# Chain of Thought (CoT) display mode. Can be "hidden", "tool_call" or "full".
cot = "full"

# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
# custom_css = "/public/test.css"

# Specify additional attributes for a custom CSS file
# custom_css_attributes = "media=\"print\""

# Specify a JavaScript file that can be used to customize the user interface.
# The JavaScript file can be served from the public directory.
# custom_js = "/public/test.js"

# Specify additional attributes for custom JS file
# custom_js_attributes = "async type = \"module\""

# Custom login page image, relative to public directory or external URL
# login_page_image = "/public/custom-background.jpg"

# Custom login page image filter (Tailwind internal filters, no dark/light variants)
# login_page_image_filter = "brightness-50 grayscale"
# login_page_image_dark_filter = "contrast-200 blur-sm"

# Specify a custom meta image url.
# custom_meta_image_url = "https://chainlit-cloud.s3.eu-west-3.amazonaws.com/logo/chainlit_banner.png"

# Specify a custom build directory for the frontend.
# This can be used to customize the frontend code.
# Be careful: If this is a relative path, it should not start with a slash.
# custom_build = "./public/build"

# Specify optional one or more custom links in the header.
# [[UI.header_links]]
#     name = "Issues"
#     display_name = "Report Issue"
#     icon_url = "https://avatars.githubusercontent.com/u/128686189?s=200&v=4"
#     url = "https://github.com/Chainlit/chainlit/issues"

[meta]
generated_by = "2.5.5"
