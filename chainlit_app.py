#!/usr/bin/env python3
"""
Chainlit Web 前端集成 - 符合官方最佳实践
"""

import os
import asyncio
from typing import Optional

# 禁用 LangSmith 追踪以避免错误
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""

import chainlit as cl
from langchain_core.messages import HumanMessage

# 导入主程序的初始化函数
from main import initialize_agent


@cl.on_chat_start
async def on_chat_start():
    """
    Chainlit 会话开始时初始化 Agent
    """
    try:
        # 初始化 Agent
        app, tools, session_manager = await initialize_agent()
        
        # 将 Agent 相关对象存储到用户会话中
        cl.user_session.set("app", app)
        cl.user_session.set("tools", tools)
        cl.user_session.set("session_manager", session_manager)
        
        # 发送欢迎消息
        await cl.Message(
            content="🤖 **LangGraph Agent 已启动！**\n\n我可以帮您处理各种任务，包括：\n- 🔍 网络搜索和信息检索\n- 🧠 复杂逻辑推理和分析\n- 📊 数据可视化和图表生成\n- 💻 代码编写和技术支持\n- 🌐 网页自动化操作\n\n请告诉我您需要什么帮助？"
        ).send()
        
    except Exception as e:
        await cl.Message(
            content=f"❌ **初始化失败**: {str(e)}\n\n请检查配置文件并重试。"
        ).send()


@cl.on_message
async def on_message(message: cl.Message):
    """
    处理用户消息 - 符合 Chainlit 官方最佳实践
    """
    # 从用户会话中获取 Agent 相关对象
    app = cl.user_session.get("app")
    session_manager = cl.user_session.get("session_manager")
    
    if not app or not session_manager:
        await cl.Message(
            content="❌ **Agent 未正确初始化**\n\n请刷新页面重试。"
        ).send()
        return
    
    # 使用 Chainlit session id 作为 thread_id，确保多用户隔离
    thread_id = cl.context.session.id
    config = {"configurable": {"thread_id": thread_id}}
    
    # 创建 Chainlit 回调处理器
    cb = cl.LangchainCallbackHandler()
    
    # 创建消息对象用于流式输出
    final_answer = cl.Message(content="")
    
    try:
        # 使用 LangGraph 的流式输出 - 符合官方文档建议
        async for msg_obj, metadata in app.astream(
            {"messages": [HumanMessage(content=message.content)]},
            stream_mode="messages",
            config={**config, "callbacks": [cb]}
        ):
            # 过滤并输出最终回复 - 只显示 AI 的最终回答
            if (
                hasattr(msg_obj, "content")
                and msg_obj.content
                and not isinstance(msg_obj, HumanMessage)
                and metadata.get("langgraph_node") != "tools"  # 排除工具调用的中间输出
            ):
                await final_answer.stream_token(msg_obj.content)
        
        # 发送最终消息
        await final_answer.send()
        
    except Exception as e:
        await cl.Message(
            content=f"❌ **处理消息时出错**: {str(e)}\n\n请重试或联系管理员。"
        ).send()


if __name__ == "__main__":
    # 用于调试的入口点
    from chainlit.cli import run_chainlit
    run_chainlit(__file__)
